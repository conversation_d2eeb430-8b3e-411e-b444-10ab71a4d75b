import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from '@tanstack/react-router';
import { DataTableStateEvent } from 'primereact/datatable';
import { DataGrid, ColumnConfig } from '@/components/ui/DataGrid/DataGrid';
import Button from '@/components/ui/Button/Button';
import Modal from '@/components/ui/Modal/Modal';
import Card from '@/components/ui/Card/Card';
import Toast, { ToastRef } from '@/components/ui/Toast/Toast';
import { UserResponse, Role } from '@/types/api/user';
import {
  useDeleteUser,
  useEnableUser,
  useDisableUser
} from '@/hooks/useUser';
import { DepartmentService } from '@/services/api/departmentService';
import { DesignationService } from '@/services/api/designationService';
import { ROUTES } from '@/constants/routes.constant';
import './UserDetails.css';

const UserDetails: React.FC = () => {
  const navigate = useNavigate();
  const toast = useRef<ToastRef>(null);

  // State for users data - using client-side sorting approach like other entities
  const [allUsers, setAllUsers] = useState<UserResponse[]>([]); // All users from API
  const [users, setUsers] = useState<UserResponse[]>([]); // Displayed users (sorted & paginated)
  const [totalRecords, setTotalRecords] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(true);
  const [currentPage, setCurrentPage] = useState<number>(0);
  const [pageSize, setPageSize] = useState<number>(10);
  const [sortField, setSortField] = useState<string>('name');
  const [sortOrder, setSortOrder] = useState<number>(1); // 1 for ascending, -1 for descending

  // Modal states
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState<boolean>(false);
  const [userToDelete, setUserToDelete] = useState<UserResponse | null>(null);

  // Mutation hooks
  const deleteUserMutation = useDeleteUser();
  const enableUserMutation = useEnableUser();
  const disableUserMutation = useDisableUser();

  // Caching for department and designation names
  const [departmentCache, setDepartmentCache] = useState<Map<number, string>>(new Map());
  const [designationCache, setDesignationCache] = useState<Map<number, string>>(new Map());

  // Helper function to fetch department name
  const fetchDepartmentName = async (departmentId: number): Promise<string> => {
    if (departmentCache.has(departmentId)) {
      return departmentCache.get(departmentId)!;
    }

    try {
      const department = await DepartmentService.getDepartmentById(departmentId);
      const name = department.name || 'Unknown Department';
      setDepartmentCache(prev => new Map(prev.set(departmentId, name)));
      return name;
    } catch (error) {
      const fallbackName = 'Unknown Department';
      setDepartmentCache(prev => new Map(prev.set(departmentId, fallbackName)));
      return fallbackName;
    }
  };

  // Helper function to fetch designation name
  const fetchDesignationName = async (designationId: number): Promise<string> => {
    if (designationCache.has(designationId)) {
      return designationCache.get(designationId)!;
    }

    try {
      const designation = await DesignationService.getDesignationById(designationId.toString());
      const name = designation.data?.name || 'Unknown Designation';
      setDesignationCache(prev => new Map(prev.set(designationId, name)));
      return name;
    } catch (error) {
      const fallbackName = 'Unknown Designation';
      setDesignationCache(prev => new Map(prev.set(designationId, fallbackName)));
      return fallbackName;
    }
  };

  // Component for displaying department name
  const DepartmentNameCell: React.FC<{ departmentId?: number }> = ({ departmentId }) => {
    const [name, setName] = useState<string>('Loading...');

    useEffect(() => {
      if (!departmentId) {
        setName('No department');
        return;
      }

      fetchDepartmentName(departmentId).then(setName);
    }, [departmentId]);

    return <div className="department-cell">{name}</div>;
  };

  // Component for displaying designation name
  const DesignationNameCell: React.FC<{ designationId?: number }> = ({ designationId }) => {
    const [name, setName] = useState<string>('Loading...');

    useEffect(() => {
      if (!designationId) {
        setName('No designation');
        return;
      }

      fetchDesignationName(designationId).then(setName);
    }, [designationId]);

    return <div className="designation-cell">{name}</div>;
  };

  // Fetch users from API (only called once)
  const fetchUsers = async () => {
    setLoading(true);
    try {
      // Import UserService to call directly
      const { UserService } = await import('@/services/api/userService');

      // Get all users without pagination/sorting (let frontend handle it)
      const userResponses = await UserService.getUsers();

      // Transform the data to match our interface
      const transformedUsers: UserResponse[] = userResponses.map((user: any) => ({
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        enabled: user.enabled,
        phoneNumber: user.phoneNumber,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        createdBy: user.createdBy,
        updatedBy: user.updatedBy,
        departmentId: user.departmentId,
        departmentName: user.departmentName,
        designationId: user.designationId,
        designationName: user.designationName,
        organizationId: user.organizationId,
        employeeId: user.employeeId,
        joiningDate: user.joiningDate,
        terminationDate: user.terminationDate,
        reportingManagerId: user.reportingManagerId,
        reportingManagerName: user.reportingManagerName,
      }));

      setAllUsers(transformedUsers);
      setTotalRecords(transformedUsers.length);

      // Apply initial sorting and pagination
      applySortingAndPagination(transformedUsers, sortField, sortOrder, currentPage, pageSize);
    } catch (error) {
      toast.current?.showError('Failed to fetch users');
    } finally {
      setLoading(false);
    }
  };

  // Apply sorting and pagination to the data
  const applySortingAndPagination = (
    data: UserResponse[],
    field: string,
    order: number,
    page: number,
    size: number
  ) => {
    // Sort the data
    const sortedData = [...data].sort((a, b) => {
      const aValue = getNestedValue(a, field);
      const bValue = getNestedValue(b, field);

      if (aValue === null || aValue === undefined) return 1;
      if (bValue === null || bValue === undefined) return -1;

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return order === 1 ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
      }

      if (aValue < bValue) return order === 1 ? -1 : 1;
      if (aValue > bValue) return order === 1 ? 1 : -1;
      return 0;
    });

    // Apply pagination
    const startIndex = page * size;
    const endIndex = startIndex + size;
    const paginatedData = sortedData.slice(startIndex, endIndex);

    setUsers(paginatedData);
  };

  // Helper function to get nested object values
  const getNestedValue = (obj: any, path: string): any => {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  };

  // Handle page change
  const handlePageChange = (event: { first: number; rows: number; page: number }) => {
    const newPage = event.page;
    const newPageSize = event.rows;

    setCurrentPage(newPage);
    setPageSize(newPageSize);

    applySortingAndPagination(allUsers, sortField, sortOrder, newPage, newPageSize);
  };

  // Handle sorting
  const handleSort = (event: DataTableStateEvent) => {
    const field = event.sortField || 'name';
    const order = event.sortOrder || 1;

    setSortField(field);
    setSortOrder(order);

    applySortingAndPagination(allUsers, field, order, currentPage, pageSize);
  };

  // Load data on component mount
  useEffect(() => {
    fetchUsers();
  }, []);

  // Re-apply sorting and pagination when allUsers changes
  useEffect(() => {
    if (allUsers.length > 0) {
      applySortingAndPagination(allUsers, sortField, sortOrder, currentPage, pageSize);
    }
  }, [allUsers, sortField, sortOrder, currentPage, pageSize]);

  // Handle add user
  const handleAdd = () => {
    navigate({ to: ROUTES.PRIVATE.APP + ROUTES.PRIVATE.ADD_USER });
  };

  // Handle edit user
  const handleEdit = (user: UserResponse) => {
    navigate({
      to: ROUTES.PRIVATE.APP + ROUTES.PRIVATE.EDIT_USER,
      search: { id: user.id.toString() }
    });
  };

  // Handle delete click
  const handleDeleteClick = (user: UserResponse) => {
    setUserToDelete(user);
    setIsDeleteModalOpen(true);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    if (!userToDelete) return;

    try {
      await deleteUserMutation.mutateAsync(userToDelete.id);
      toast.current?.showSuccess('User deleted successfully');

      // Refresh the data
      await fetchUsers();

      // Close the modal
      setIsDeleteModalOpen(false);
      setUserToDelete(null);
    } catch (error) {
      toast.current?.showError('Failed to delete user');
    }
  };

  // Handle enable/disable user
  const handleToggleUserStatus = async (user: UserResponse) => {
    try {
      if (user.enabled) {
        await disableUserMutation.mutateAsync(user.id);
        toast.current?.showSuccess('User disabled successfully');
      } else {
        await enableUserMutation.mutateAsync(user.id);
        toast.current?.showSuccess('User enabled successfully');
      }

      // Refresh the data
      await fetchUsers();
    } catch (error) {
      toast.current?.showError('Failed to update user status');
    }
  };

  // Get role display name
  const getRoleDisplayName = (role: Role): string => {
    switch (role) {
      case Role.ROLE_USER:
        return 'User';
      case Role.ROLE_ADMIN:
        return 'Admin';
      case Role.ROLE_SUPER_ADMIN:
        return 'Super Admin';
      default:
        return role;
    }
  };

  // DataGrid columns configuration
  const columns: ColumnConfig[] = [
    {
      field: 'name',
      header: 'Name',
      sortable: true
    },
    {
      field: 'email',
      header: 'Email',
      sortable: true
    },
    {
      field: 'role',
      header: 'Role',
      sortable: true,
      body: (rowData: UserResponse) => (
        <span className="role-badge">
          {getRoleDisplayName(rowData.role)}
        </span>
      )
    },
    {
      field: 'departmentName',
      header: 'Department',
      sortable: false, // Disable sorting since we're fetching names dynamically
      body: (rowData: UserResponse) => (
        <DepartmentNameCell departmentId={rowData.departmentId} />
      )
    },
    {
      field: 'designationName',
      header: 'Designation',
      sortable: false, // Disable sorting since we're fetching names dynamically
      body: (rowData: UserResponse) => (
        <DesignationNameCell designationId={rowData.designationId} />
      )
    },
    {
      field: 'enabled',
      header: 'Status',
      sortable: true,
      body: (rowData: UserResponse) => (
        <span className={`status-badge ${rowData.enabled ? 'enabled' : 'disabled'}`}>
          {rowData.enabled ? 'Enabled' : 'Disabled'}
        </span>
      )
    },
    {
      field: 'actions',
      header: 'Actions',
      sortable: false,
      body: (rowData: UserResponse) => (
        <div className="flex gap-2 justify-content-center">
          <Button
            icon="pi pi-pencil"
            variant="outline"
            size="small"
            onClick={() => handleEdit(rowData)}
            aria-label="Edit"
          />
          <Button
            icon={rowData.enabled ? "pi pi-ban" : "pi pi-check"}
            variant="outline"
            size="small"
            onClick={() => handleToggleUserStatus(rowData)}
            aria-label={rowData.enabled ? "Disable" : "Enable"}
            className={rowData.enabled ? "p-button-warning" : "p-button-success"}
          />
          <Button
            icon="pi pi-trash"
            variant="outline"
            size="small"
            onClick={() => handleDeleteClick(rowData)}
            aria-label="Delete"
            className="p-button-danger"
          />
        </div>
      )
    }
  ];

  return (
    <div className="user_details p-4">
      <Toast ref={toast} position="top-right" />
      <Card title="User Management" variant="elevated" className="mb-4">
        <div className="flex justify-content-end mb-3">
          <Button
            variant="primary"
            leftIcon={<i className="pi pi-plus"></i>}
            onClick={handleAdd}
          >
            Add User
          </Button>
        </div>
        <DataGrid
          value={users}
          columns={columns}
          totalRecords={totalRecords}
          loading={loading}
          onPage={handlePageChange}
          onSort={handleSort}
          rows={pageSize}
          rowsPerPageOptions={[10, 25, 50]}
          showGridLines={true}
          stripedRows={true}
          sortField={sortField}
          sortOrder={sortOrder}
        />
      </Card>

      {/* Delete Confirmation Modal */}
      <Modal
        visible={isDeleteModalOpen}
        onHide={() => setIsDeleteModalOpen(false)}
        header="Confirm Delete"
        modalProps={{ style: { width: '30vw' } }}
      >
        <div className="confirmation-content">
          <i className="pi pi-exclamation-triangle mr-3" style={{ fontSize: '2rem', color: 'var(--orange-500)' }}></i>
          <span>
            Are you sure you want to delete user <strong>{userToDelete?.name}</strong>?
            This action cannot be undone.
          </span>
        </div>
        <div className="flex justify-content-end gap-2 mt-4">
          <Button
            variant="outline"
            onClick={() => setIsDeleteModalOpen(false)}
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            className="p-button-danger"
            onClick={handleDeleteConfirm}
            isLoading={deleteUserMutation.isPending}
          >
            Delete
          </Button>
        </div>
      </Modal>
    </div>
  );
};

export default UserDetails;
