import { useState } from 'react';
import { useF<PERSON><PERSON><PERSON><PERSON><PERSON>, Controller, RegisterOptions } from 'react-hook-form';
import { AutoComplete } from 'primereact/autocomplete';
import { UserService } from '@/services/api/userService';
import { UserResponse } from '@/types/api/user';

interface RHFAutoCompleteProps {
  name: string;
  label?: string;
  placeholder?: string;
  icon?: string;
  disabled?: boolean;
  className?: string;
  rules?: RegisterOptions;
}

interface UserOption {
  label: string;
  value: number;
  email: string;
}

export const RHFAutoComplete = ({
  name,
  label,
  rules,
  placeholder,
  icon,
  ...rest
}: RHFAutoCompleteProps) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  const [suggestions, setSuggestions] = useState<UserOption[]>([]);

  const error = errors[name]?.message as string | undefined;

  // Search for users
  const searchUsers = async (query: string) => {
    if (!query || query.length < 2) {
      setSuggestions([]);
      return;
    }

    try {
      const users: UserResponse[] = await UserService.searchUsers(query);
      const userOptions: UserOption[] = users.map(user => ({
        label: `${user.name} (${user.email})`,
        value: user.id,
        email: user.email
      }));
      setSuggestions(userOptions);
    } catch (error) {
      console.error('Error searching users:', error);
      setSuggestions([]);
    }
  };

  // Handle search input
  const handleSearch = (event: { query: string }) => {
    searchUsers(event.query);
  };

  // Custom item template for dropdown
  const itemTemplate = (item: UserOption) => {
    return (
      <div className="flex align-items-center">
        <i className="pi pi-user mr-2"></i>
        <div>
          <div className="font-medium">{item.label.split(' (')[0]}</div>
          <div className="text-sm text-color-secondary">{item.email}</div>
        </div>
      </div>
    );
  };

  return (
    <div className="field mb-3">
      {label && <label htmlFor={name} className="block mb-1">{label}</label>}
      <Controller
        name={name}
        control={control}
        rules={rules}
        render={({ field }) => (
          <div className={`p-inputgroup ${error ? 'p-invalid' : ''}`}>
            {icon && <span className="p-inputgroup-addon"><i className={`pi ${icon}`}></i></span>}
            <AutoComplete
              id={name}
              value={field.value ? suggestions.find(s => s.value === field.value) : undefined}
              suggestions={suggestions}
              completeMethod={handleSearch}
              onChange={(e) => {
                // Handle both selection and clearing
                if (e.value && typeof e.value === 'object' && 'value' in e.value) {
                  field.onChange(e.value.value);
                } else {
                  field.onChange(null);
                }
              }}
              onBlur={field.onBlur}
              placeholder={placeholder || `Search ${label}`}
              className={`w-full ${error ? 'p-invalid' : ''}`}
              inputClassName="w-full"
              itemTemplate={itemTemplate}
              field="label"
              dropdown
              forceSelection={false}
              emptyMessage="No users found"
              disabled={rest.disabled}
              {...rest}
            />
          </div>
        )}
      />
      {error && <small className="p-error">{error}</small>}
    </div>
  );
};
