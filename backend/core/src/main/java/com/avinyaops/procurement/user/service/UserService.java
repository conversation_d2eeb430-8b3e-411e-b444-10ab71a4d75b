package com.avinyaops.procurement.user.service;

import com.avinyaops.procurement.user.dto.UserCreateRequest;
import com.avinyaops.procurement.user.dto.UserResponse;
import com.avinyaops.procurement.user.dto.UserUpdateRequest;
import com.avinyaops.procurement.user.model.Role;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface UserService {
    UserResponse createUser(UserCreateRequest request);

    UserResponse updateUser(Long id, UserUpdateRequest request);

    void deleteUser(Long id);

    UserResponse getUser(Long id);

    List<UserResponse> getAllUsers();

    UserResponse enableUser(Long id);

    UserResponse disableUser(Long id);

    List<UserResponse> searchUsers(String searchTerm, Role role, Boolean enabled);

    boolean isCurrentUser(Long userId);
}