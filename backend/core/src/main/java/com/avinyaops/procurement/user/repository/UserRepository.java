package com.avinyaops.procurement.user.repository;

import com.avinyaops.procurement.user.model.Role;
import com.avinyaops.procurement.user.model.User;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    Optional<User> findByEmail(String email);

    boolean existsByEmail(String email);

    @Query("SELECT u FROM User u WHERE " +
            "(:searchTerm IS NULL OR " +
            "LOWER(u.email) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
            "LOWER(u.name) LIKE LOWER(CONCAT('%', :searchTerm, '%'))) AND " +
            "(:role IS NULL OR u.role = :role) AND " +
            "(:enabled IS NULL OR u.enabled = :enabled)")
    List<User> searchUsers(
            @Param("searchTerm") String searchTerm,
            @Param("role") Role role,
            @Param("enabled") Boolean enabled);

    boolean existsByDepartmentId(@Param("departmentId") Long departmentId);

    boolean existsByDesignationId(@Param("designationId") Long designationId);
}