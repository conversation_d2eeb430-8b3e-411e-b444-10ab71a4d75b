package com.avinyaops.procurement.user.controller;

import com.avinyaops.procurement.user.dto.UserCreateRequest;
import com.avinyaops.procurement.user.dto.UserResponse;
import com.avinyaops.procurement.user.dto.UserUpdateRequest;
import com.avinyaops.procurement.user.exception.UserAlreadyExistsException;
import com.avinyaops.procurement.user.exception.UserNotFoundException;
import com.avinyaops.procurement.user.model.Role;
import com.avinyaops.procurement.user.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/users")
@RequiredArgsConstructor
public class UserController {

    private final UserService userService;

    @PostMapping
    // @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Create a new user", description = "Creates a new user with the provided details")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "User created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input data"),
        @ApiResponse(responseCode = "409", description = "User already exists"),
        @ApiResponse(responseCode = "403", description = "Not authorized to create users")
    })
    public ResponseEntity<UserResponse> createUser(@Valid @RequestBody UserCreateRequest request) {
        try {
            UserResponse response = userService.createUser(request);
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
        } catch (UserAlreadyExistsException e) {
            throw e;
        }
    }

    @PutMapping("/{id}")
    // @PreAuthorize("hasRole('ADMIN') or @userService.isCurrentUser(#id)")
    @Operation(summary = "Update a user", description = "Updates an existing user's details")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User updated successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input data"),
        @ApiResponse(responseCode = "404", description = "User not found"),
        @ApiResponse(responseCode = "403", description = "Not authorized to update user")
    })
    public ResponseEntity<UserResponse> updateUser(
            @Parameter(description = "User ID") @PathVariable Long id,
            @Valid @RequestBody UserUpdateRequest request) {
        try {
            UserResponse response = userService.updateUser(id, request);
            return ResponseEntity.ok(response);
        } catch (UserNotFoundException e) {
            throw e;
        }
    }

    @DeleteMapping("/{id}")
    // @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Delete a user", description = "Deletes a user by ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "User deleted successfully"),
        @ApiResponse(responseCode = "404", description = "User not found"),
        @ApiResponse(responseCode = "403", description = "Not authorized to delete users")
    })
    public ResponseEntity<Void> deleteUser(@Parameter(description = "User ID") @PathVariable Long id) {
        try {
            userService.deleteUser(id);
            return ResponseEntity.noContent().build();
        } catch (UserNotFoundException e) {
            throw e;
        }
    }

    @GetMapping("/{id}")
    // @PreAuthorize("hasRole('ADMIN') or @userService.isCurrentUser(#id)")
    @Operation(summary = "Get user by ID", description = "Retrieves a user's details by ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User found"),
        @ApiResponse(responseCode = "404", description = "User not found"),
        @ApiResponse(responseCode = "403", description = "Not authorized to view user")
    })
    public ResponseEntity<UserResponse> getUser(@Parameter(description = "User ID") @PathVariable Long id) {
        try {
            UserResponse response = userService.getUser(id);
            return ResponseEntity.ok(response);
        } catch (UserNotFoundException e) {
            throw e;
        }
    }

    @GetMapping
    // @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get all users", description = "Retrieves a paginated list of all users")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Users retrieved successfully"),
        @ApiResponse(responseCode = "403", description = "Not authorized to view users")
    })
    public ResponseEntity<List<UserResponse>> getAllUsers() {
        List<UserResponse> users = userService.getAllUsers();
        return ResponseEntity.ok(users);
    }

    @PatchMapping("/{id}/enable")
    // @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Enable a user", description = "Enables a disabled user account")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User enabled successfully"),
        @ApiResponse(responseCode = "404", description = "User not found"),
        @ApiResponse(responseCode = "403", description = "Not authorized to enable users")
    })
    public ResponseEntity<UserResponse> enableUser(@Parameter(description = "User ID") @PathVariable Long id) {
        try {
            UserResponse response = userService.enableUser(id);
            return ResponseEntity.ok(response);
        } catch (UserNotFoundException e) {
            throw e;
        }
    }

    @PatchMapping("/{id}/disable")
    // @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Disable a user", description = "Disables a user account")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User disabled successfully"),
        @ApiResponse(responseCode = "404", description = "User not found"),
        @ApiResponse(responseCode = "403", description = "Not authorized to disable users")
    })
    public ResponseEntity<UserResponse> disableUser(@Parameter(description = "User ID") @PathVariable Long id) {
        try {
            UserResponse response = userService.disableUser(id);
            return ResponseEntity.ok(response);
        } catch (UserNotFoundException e) {
            throw e;
        }
    }

    @GetMapping("/search")
    // @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Search users", description = "Search users by various criteria")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Search completed successfully"),
        @ApiResponse(responseCode = "403", description = "Not authorized to search users")
    })
    public ResponseEntity<List<UserResponse>> searchUsers(
            @Parameter(description = "Search term") @RequestParam(required = false) String searchTerm,
            @Parameter(description = "Role filter") @RequestParam(required = false) Role role,
            @Parameter(description = "Enabled status filter") @RequestParam(required = false) Boolean enabled) {
        List<UserResponse> users = userService.searchUsers(searchTerm, role, enabled);
        return ResponseEntity.ok(users);
    }
    //TODO: Add pagination everywhere later
} 