package com.avinyaops.procurement.user.service;

import com.avinyaops.procurement.organization.department.repository.DepartmentRepository;
import com.avinyaops.procurement.organization.designation.repository.DesignationRepository;
import com.avinyaops.procurement.organization.exception.OrganizationNotFoundException;
import com.avinyaops.procurement.organization.repository.OrganizationRepository;
import com.avinyaops.procurement.user.dto.UserCreateRequest;
import com.avinyaops.procurement.user.dto.UserResponse;
import com.avinyaops.procurement.user.dto.UserUpdateRequest;
import com.avinyaops.procurement.user.exception.UserAlreadyExistsException;
import com.avinyaops.procurement.user.exception.UserNotFoundException;
import com.avinyaops.procurement.user.model.Role;
import com.avinyaops.procurement.user.model.User;
import com.avinyaops.procurement.user.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Transactional
@Slf4j
public class UserServiceImpl implements UserService {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    private final OrganizationRepository organizationRepository;
    private final DepartmentRepository departmentRepository;
    private final DesignationRepository designationRepository;

    @Override
    public UserResponse createUser(UserCreateRequest request) {
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new UserAlreadyExistsException(request.getEmail());
        }

        User user = User.builder()
                .email(request.getEmail())
                .password(passwordEncoder.encode(request.getPassword()))
                .name(request.getName())
                .phoneNumber(request.getPhoneNumber())
                .organization(organizationRepository.findById(request.getOrganizationId()).orElseThrow(() -> new OrganizationNotFoundException(request.getOrganizationId())))
                .department(departmentRepository.findById(request.getDepartmentId()).orElseThrow())
                .designation(designationRepository.findById(request.getDesignationId()).orElseThrow())
                .role(request.getRole())
                .enabled(true)
                .reportingManager(userRepository.findById(request.getReportingManagerId()).orElseThrow())
                .build();

        user = userRepository.save(user);
        return mapToResponse(user);
    }

    @Override
    public UserResponse updateUser(Long id, UserUpdateRequest request) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new UserNotFoundException(id));

        if (request.getEmail() != null && !request.getEmail().equals(user.getEmail())) {
            if (userRepository.existsByEmail(request.getEmail())) {
                throw new UserAlreadyExistsException(request.getEmail());
            }
            user.setEmail(request.getEmail());
        }

        if (request.getPassword() != null) {
            user.setPassword(passwordEncoder.encode(request.getPassword()));
        }

        if (request.getName() != null) {
            user.setName(request.getName());
        }

        if (request.getRole() != null) {
            user.setRole(request.getRole());
        }

        if(request.getPhoneNumber() != null) {
            user.setPhoneNumber(request.getPhoneNumber());
        }

        if(request.getDepartmentId() != null) {
            user.setDepartment(departmentRepository.findById(request.getDepartmentId()).orElseThrow());
        }
        if(request.getDesignationId() != null) {
            user.setDesignation(designationRepository.findById(request.getDesignationId()).orElseThrow());
        }

        if (request.getEnabled() != null) {
            user.setEnabled(request.getEnabled());
        }
        
        log.info("readf: "+ request.getReportingManagerId() + " " + user.getId());
        if((request.getReportingManagerId() != null) && (request.getReportingManagerId() != user.getId())) {
            log.info("readf: "+ request.getReportingManagerId() + " " + user.getId());
            user.setReportingManager(userRepository.findById(request.getReportingManagerId()).orElseThrow());
        }

        user = userRepository.save(user);
        return mapToResponse(user);
    }

    @Override
    public void deleteUser(Long id) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new UserNotFoundException(id));
        user.softDelete();
        userRepository.save(user);
    }

    @Override
    @Transactional(readOnly = true)
    public UserResponse getUser(Long id) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new UserNotFoundException(id));
        return mapToResponse(user);
    }

    @Override
    @Transactional(readOnly = true)
    public List<UserResponse> getAllUsers() {
        // return userRepository.findAll(pageable)
        //         .map(this::mapToResponse);
        return userRepository.findAll().stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public UserResponse enableUser(Long id) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new UserNotFoundException(id));
        user.setEnabled(true);
        user = userRepository.save(user);
        return mapToResponse(user);
    }

    @Override
    public UserResponse disableUser(Long id) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new UserNotFoundException(id));
        user.setEnabled(false);
        user = userRepository.save(user);
        return mapToResponse(user);
    }

    @Override
    @Transactional(readOnly = true)
    public List<UserResponse> searchUsers(String searchTerm, Role role, Boolean enabled) {
        return userRepository.searchUsers(searchTerm, role, enabled)
                .stream().map(this::mapToResponse).collect(Collectors.toList());
    }

    @Override
    public boolean isCurrentUser(Long userId) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        log.info("Authentication: {}", authentication);
        if (authentication == null || !authentication.isAuthenticated()) {
            return false;
        }
        return userRepository.findById(userId)
                .map(user -> user.getEmail().equals(authentication.getName()))
                .orElse(false);
    }

    private UserResponse mapToResponse(User user) {
        return UserResponse.builder()
                .id(user.getId())
                .email(user.getEmail())
                .name(user.getName())
                .role(user.getRole())
                .enabled(user.isEnabled())
                .phoneNumber(user.getPhoneNumber())
                .reportingManagerId(Optional.ofNullable(user.getReportingManager()).map(User::getId).orElse(null))
                .build();
    }
}